<?php
/**
 * Undefined Function Fix Test
 * 
 * This script tests the fix for undefined function errors in ChatGABI
 * Access via: http://localhost/swifmind-local/wordpress/wp-content/themes/businesscraft-ai/test-undefined-function-fix.php
 */

// Load WordPress
$wp_load_paths = [
    dirname(dirname(dirname(__DIR__))) . '/wp-load.php',
    '../../../wp-load.php'
];

$wp_loaded = false;
foreach ($wp_load_paths as $path) {
    if (file_exists($path)) {
        require_once($path);
        $wp_loaded = true;
        break;
    }
}

if (!$wp_loaded) {
    die('Could not load WordPress');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ChatGABI Undefined Function Fix Test</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', <PERSON><PERSON>, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 900px; margin: 0 auto; background: white; padding: 24px; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin-bottom: 24px; padding: 16px; border: 1px solid #e0e0e0; border-radius: 8px; }
        .test-section h2 { margin-top: 0; color: #2563eb; }
        .success { color: #059669; font-weight: 500; }
        .error { color: #dc2626; font-weight: 500; }
        .warning { color: #d97706; font-weight: 500; }
        .info { color: #2563eb; }
        pre { background: #f8f9fa; padding: 12px; border-radius: 6px; overflow-x: auto; font-size: 12px; border: 1px solid #e0e0e0; }
        .test-result { padding: 8px; border-radius: 4px; margin: 4px 0; }
        .test-result.pass { background: #d1fae5; border: 1px solid #10b981; }
        .test-result.fail { background: #fee2e2; border: 1px solid #ef4444; }
        .test-result.warn { background: #fef3c7; border: 1px solid #f59e0b; }
        .summary { background: #f0f9ff; border: 1px solid #0ea5e9; padding: 16px; border-radius: 8px; margin-top: 24px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 ChatGABI Undefined Function Fix Test</h1>
        <p class="info">Testing the fix for undefined function errors in the ChatGABI theme.</p>
        
        <div class="test-section">
            <h2>1. WordPress Environment</h2>
            <div class="test-result pass">✅ WordPress loaded successfully</div>
            <div class="info">Current theme: <strong><?php echo wp_get_theme()->get('Name'); ?></strong></div>
            <div class="info">Theme directory: <code><?php echo get_template_directory(); ?></code></div>
        </div>
        
        <div class="test-section">
            <h2>2. Location Functions Availability</h2>
            
            <?php
            // Test if the ensure function exists
            if (function_exists('chatgabi_ensure_location_functions')):
            ?>
                <div class="test-result pass">✅ chatgabi_ensure_location_functions() exists</div>
                
                <?php
                // Call the ensure function
                chatgabi_ensure_location_functions();
                ?>
                
                <div class="test-result pass">✅ Location functions ensured</div>
                
            <?php else: ?>
                <div class="test-result fail">❌ chatgabi_ensure_location_functions() not found</div>
            <?php endif; ?>
            
            <?php
            // Test each location function
            $location_functions = [
                'businesscraft_ai_get_user_ip',
                'businesscraft_ai_get_user_country', 
                'businesscraft_ai_get_user_currency'
            ];
            
            foreach ($location_functions as $func):
            ?>
                <div class="test-result <?php echo function_exists($func) ? 'pass' : 'fail'; ?>">
                    <?php echo function_exists($func) ? '✅' : '❌'; ?> 
                    <code><?php echo $func; ?>()</code> 
                    <?php echo function_exists($func) ? 'exists' : 'not found'; ?>
                </div>
            <?php endforeach; ?>
        </div>
        
        <div class="test-section">
            <h2>3. Function Behavior Test</h2>
            
            <?php if (function_exists('businesscraft_ai_get_user_ip')): ?>
                <?php
                try {
                    $user_ip = businesscraft_ai_get_user_ip();
                ?>
                    <div class="test-result pass">✅ businesscraft_ai_get_user_ip() executed successfully</div>
                    <div class="info">Returned IP: <code><?php echo htmlspecialchars($user_ip); ?></code></div>
                    
                    <?php if (filter_var($user_ip, FILTER_VALIDATE_IP) || $user_ip === 'unknown'): ?>
                        <div class="test-result pass">✅ Valid IP format returned</div>
                    <?php else: ?>
                        <div class="test-result warn">⚠️ Unusual IP format (may be expected for localhost)</div>
                    <?php endif; ?>
                    
                <?php
                } catch (Exception $e) {
                ?>
                    <div class="test-result fail">❌ Error calling businesscraft_ai_get_user_ip(): <?php echo htmlspecialchars($e->getMessage()); ?></div>
                <?php
                }
            else:
            ?>
                <div class="test-result fail">❌ Cannot test businesscraft_ai_get_user_ip() - function not available</div>
            <?php endif; ?>
            
            <?php if (function_exists('businesscraft_ai_get_user_country')): ?>
                <?php
                try {
                    $user_country = businesscraft_ai_get_user_country();
                ?>
                    <div class="test-result pass">✅ businesscraft_ai_get_user_country() executed successfully</div>
                    <div class="info">Returned Country: <code><?php echo htmlspecialchars($user_country); ?></code></div>
                    
                    <?php
                    $valid_countries = ['GH', 'KE', 'NG', 'ZA'];
                    if (in_array($user_country, $valid_countries)):
                    ?>
                        <div class="test-result pass">✅ Valid country code returned</div>
                    <?php else: ?>
                        <div class="test-result warn">⚠️ Unexpected country code (may be valid)</div>
                    <?php endif; ?>
                    
                <?php
                } catch (Exception $e) {
                ?>
                    <div class="test-result fail">❌ Error calling businesscraft_ai_get_user_country(): <?php echo htmlspecialchars($e->getMessage()); ?></div>
                <?php
                }
            else:
            ?>
                <div class="test-result fail">❌ Cannot test businesscraft_ai_get_user_country() - function not available</div>
            <?php endif; ?>
            
            <?php if (function_exists('businesscraft_ai_get_user_currency')): ?>
                <?php
                try {
                    $user_currency = businesscraft_ai_get_user_currency();
                ?>
                    <div class="test-result pass">✅ businesscraft_ai_get_user_currency() executed successfully</div>
                    <div class="info">Returned Currency: <code><?php echo htmlspecialchars(json_encode($user_currency)); ?></code></div>
                    
                    <?php if (is_array($user_currency) && isset($user_currency['code']) && isset($user_currency['symbol'])): ?>
                        <div class="test-result pass">✅ Valid currency format returned</div>
                        <div class="info">Currency Code: <strong><?php echo htmlspecialchars($user_currency['code']); ?></strong></div>
                        <div class="info">Currency Symbol: <strong><?php echo htmlspecialchars($user_currency['symbol']); ?></strong></div>
                    <?php else: ?>
                        <div class="test-result warn">⚠️ Unexpected currency format</div>
                    <?php endif; ?>
                    
                <?php
                } catch (Exception $e) {
                ?>
                    <div class="test-result fail">❌ Error calling businesscraft_ai_get_user_currency(): <?php echo htmlspecialchars($e->getMessage()); ?></div>
                <?php
                }
            else:
            ?>
                <div class="test-result fail">❌ Cannot test businesscraft_ai_get_user_currency() - function not available</div>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>4. Script Enqueuing Test</h2>
            
            <?php
            // Test if chatgabi_scripts function exists and can be called
            if (function_exists('chatgabi_scripts')):
            ?>
                <div class="test-result pass">✅ chatgabi_scripts() function exists</div>
                
                <?php
                try {
                    // Capture any output or errors
                    ob_start();
                    chatgabi_scripts();
                    $output = ob_get_clean();
                ?>
                    <div class="test-result pass">✅ chatgabi_scripts() executed without fatal errors</div>
                    
                    <?php if (!empty($output)): ?>
                        <div class="info">Function output:</div>
                        <pre><?php echo htmlspecialchars($output); ?></pre>
                    <?php endif; ?>
                    
                <?php
                } catch (Exception $e) {
                ?>
                    <div class="test-result fail">❌ Error calling chatgabi_scripts(): <?php echo htmlspecialchars($e->getMessage()); ?></div>
                <?php
                } catch (Error $e) {
                ?>
                    <div class="test-result fail">❌ Fatal error in chatgabi_scripts(): <?php echo htmlspecialchars($e->getMessage()); ?></div>
                <?php
                }
            else:
            ?>
                <div class="test-result fail">❌ chatgabi_scripts() function not found</div>
            <?php endif; ?>
        </div>
        
        <div class="test-section">
            <h2>5. Module Loading Status</h2>
            
            <?php
            // Check if paystack integration is loaded
            $paystack_file = get_template_directory() . '/inc/paystack-integration.php';
            $paystack_loaded = false;
            
            $loaded_files = get_included_files();
            foreach ($loaded_files as $file) {
                if (strpos($file, 'paystack-integration.php') !== false) {
                    $paystack_loaded = true;
                    break;
                }
            }
            ?>
            
            <div class="test-result <?php echo file_exists($paystack_file) ? 'pass' : 'fail'; ?>">
                <?php echo file_exists($paystack_file) ? '✅' : '❌'; ?> 
                Paystack integration file exists
            </div>
            
            <div class="test-result <?php echo $paystack_loaded ? 'pass' : 'warn'; ?>">
                <?php echo $paystack_loaded ? '✅' : '⚠️'; ?> 
                Paystack integration <?php echo $paystack_loaded ? 'loaded' : 'not loaded (using fallbacks)'; ?>
            </div>
            
            <?php
            // Count loaded ChatGABI modules
            $chatgabi_modules = array_filter($loaded_files, function($file) {
                return strpos($file, 'businesscraft-ai/inc/') !== false;
            });
            ?>
            
            <div class="info">ChatGABI modules loaded: <strong><?php echo count($chatgabi_modules); ?></strong></div>
        </div>
        
        <div class="test-section">
            <h2>6. Debug Mode Test</h2>
            
            <?php if (defined('WP_DEBUG') && WP_DEBUG): ?>
                <div class="test-result pass">✅ Debug mode is enabled</div>
                
                <?php
                // Test the debug localization that was causing the error
                if (function_exists('wp_localize_script')):
                ?>
                    <div class="test-result pass">✅ wp_localize_script() available</div>
                    
                    <?php
                    // Simulate the debug localization
                    chatgabi_ensure_location_functions();
                    
                    $debug_data = array(
                        'enabled' => true,
                        'userIP' => function_exists('businesscraft_ai_get_user_ip') ? businesscraft_ai_get_user_ip() : 'unknown',
                        'detectedCountry' => function_exists('businesscraft_ai_get_user_country') ? businesscraft_ai_get_user_country() : 'GH',
                        'userCurrency' => function_exists('businesscraft_ai_get_user_currency') ? businesscraft_ai_get_user_currency() : array('code' => 'GHS', 'symbol' => '₵'),
                    );
                    ?>
                    
                    <div class="test-result pass">✅ Debug data generated successfully</div>
                    <div class="info">Debug data:</div>
                    <pre><?php echo htmlspecialchars(json_encode($debug_data, JSON_PRETTY_PRINT)); ?></pre>
                    
                <?php else: ?>
                    <div class="test-result fail">❌ wp_localize_script() not available</div>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="test-result warn">⚠️ Debug mode is disabled</div>
                <div class="info">The original error only occurs in debug mode</div>
            <?php endif; ?>
        </div>
        
        <div class="summary">
            <h2>📋 Test Summary</h2>
            
            <?php
            $all_functions_exist = function_exists('businesscraft_ai_get_user_ip') && 
                                  function_exists('businesscraft_ai_get_user_country') && 
                                  function_exists('businesscraft_ai_get_user_currency');
            
            $scripts_function_works = function_exists('chatgabi_scripts');
            $ensure_function_exists = function_exists('chatgabi_ensure_location_functions');
            
            $overall_success = $all_functions_exist && $scripts_function_works && $ensure_function_exists;
            ?>
            
            <?php if ($overall_success): ?>
                <div class="test-result pass">
                    ✅ <strong>SUCCESS:</strong> All undefined function errors have been resolved
                </div>
                <div class="info">
                    <strong>Fix Details:</strong>
                    <ul>
                        <li>Location functions are now available through fallbacks</li>
                        <li>chatgabi_scripts() function executes without errors</li>
                        <li>Function existence checks prevent fatal errors</li>
                        <li>Conditional loading system preserved</li>
                    </ul>
                </div>
            <?php else: ?>
                <div class="test-result fail">
                    ❌ <strong>ISSUES DETECTED:</strong> Some functions still need attention
                </div>
                <div class="info">
                    <strong>Recommended Actions:</strong>
                    <ul>
                        <li>Check WordPress error logs for additional details</li>
                        <li>Verify theme file permissions</li>
                        <li>Ensure all required modules are accessible</li>
                        <li>Test with different user contexts (logged in/out)</li>
                    </ul>
                </div>
            <?php endif; ?>
            
            <div class="info">
                <strong>Next Steps:</strong>
                <ul>
                    <li>Test website loading without fatal errors</li>
                    <li>Verify script enqueuing functionality</li>
                    <li>Check debug mode operations</li>
                    <li>Monitor error logs for any remaining issues</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
