<?php
/**
 * Enhanced Template Preview System for ChatGABI
 * WordPress Theme-Style Preview with Complete Interface Rendering
 * 
 * @package ChatGABI
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Generate comprehensive template preview with full interface rendering
 */
function chatgabi_generate_enhanced_template_preview($template_id, $preview_options = array()) {
    global $wpdb;
    
    // Get template data
    $template = chatgabi_get_template_by_id($template_id);
    if (!$template) {
        return new WP_Error('template_not_found', __('Template not found', 'chatgabi'));
    }
    
    // Get user context for personalization
    $user_id = get_current_user_id();
    $user_country = get_user_meta($user_id, 'businesscraft_ai_country', true) ?: 'GH';
    $user_industry = get_user_meta($user_id, 'businesscraft_ai_industry', true) ?: 'general';
    $user_language = get_user_meta($user_id, 'bcai_preferred_language', true) ?: 'en';
    
    // Generate sample data based on template type and user context
    $sample_data = chatgabi_generate_template_sample_data($template, $user_country, $user_industry);
    
    // Build enhanced preview HTML
    $preview_html = chatgabi_build_template_preview_html($template, $sample_data, $preview_options);
    
    return array(
        'template_id' => $template_id,
        'preview_html' => $preview_html,
        'sample_data' => $sample_data,
        'interactive_elements' => chatgabi_get_template_interactive_elements($template),
        'customization_options' => chatgabi_get_template_customization_options($template),
        'export_formats' => array('pdf', 'docx', 'html'),
        'estimated_completion_time' => chatgabi_estimate_template_completion_time($template)
    );
}

/**
 * Generate realistic sample data for template preview
 */
function chatgabi_generate_template_sample_data($template, $user_country, $user_industry) {
    // Get African Context Engine for realistic examples
    $african_context = null;
    if (class_exists('BusinessCraft_African_Context_Engine')) {
        $african_context = new BusinessCraft_African_Context_Engine();
    }
    
    $country_name = chatgabi_get_country_name_from_code($user_country);
    
    // Base sample data structure
    $sample_data = array(
        'business_name' => chatgabi_get_sample_business_name($user_industry, $user_country),
        'business_description' => chatgabi_get_sample_business_description($user_industry, $user_country),
        'target_market' => $country_name,
        'industry_sector' => ucfirst($user_industry),
        'founder_name' => chatgabi_get_sample_founder_name($user_country),
        'company_address' => chatgabi_get_sample_address($user_country),
        'financial_projections' => chatgabi_get_sample_financial_data($user_country),
        'market_analysis' => chatgabi_get_sample_market_data($user_industry, $user_country),
        'competitive_landscape' => chatgabi_get_sample_competitor_data($user_industry, $user_country)
    );
    
    // Add African Context Engine examples if available
    if ($african_context) {
        $market_examples = $african_context->generate_market_examples($user_country, $user_industry);
        $sample_data['local_examples'] = $market_examples;
        $sample_data['cultural_context'] = $african_context->get_country_context($user_country);
    }
    
    return $sample_data;
}

/**
 * Build comprehensive template preview HTML with full interface
 */
function chatgabi_build_template_preview_html($template, $sample_data, $options = array()) {
    $template_type = $template->category_slug ?? 'business-planning';
    
    // Start building preview HTML
    $html = '<div class="chatgabi-template-preview-enhanced" data-template-id="' . esc_attr($template->id) . '">';
    
    // Preview header with template info
    $html .= chatgabi_build_preview_header($template, $sample_data);
    
    // Template content sections based on type
    switch ($template_type) {
        case 'business-planning':
            $html .= chatgabi_build_business_plan_preview($template, $sample_data);
            break;
        case 'marketing-sales':
            $html .= chatgabi_build_marketing_strategy_preview($template, $sample_data);
            break;
        case 'financial-analysis':
            $html .= chatgabi_build_financial_analysis_preview($template, $sample_data);
            break;
        default:
            $html .= chatgabi_build_generic_template_preview($template, $sample_data);
    }
    
    // Interactive elements and customization panel
    $html .= chatgabi_build_preview_interactive_panel($template, $sample_data);
    
    // Preview footer with actions
    $html .= chatgabi_build_preview_footer($template);
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Build preview header with template information
 */
function chatgabi_build_preview_header($template, $sample_data) {
    $html = '<div class="preview-header-enhanced">';
    $html .= '<div class="template-branding">';
    $html .= '<div class="template-logo">';
    $html .= '<img src="' . get_template_directory_uri() . '/assets/images/chatgabi-logo.png" alt="ChatGABI" class="preview-logo" />';
    $html .= '</div>';
    $html .= '<div class="template-meta-info">';
    $html .= '<h1 class="template-title-preview">' . esc_html($template->title) . '</h1>';
    $html .= '<div class="template-subtitle">' . esc_html($template->description) . '</div>';
    $html .= '<div class="template-context-tags">';
    $html .= '<span class="context-tag country-tag">' . esc_html($sample_data['target_market']) . '</span>';
    $html .= '<span class="context-tag industry-tag">' . esc_html($sample_data['industry_sector']) . '</span>';
    $html .= '<span class="context-tag language-tag">' . esc_html($template->language_code) . '</span>';
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</div>';
    
    // Progress indicator
    $html .= '<div class="template-progress-indicator">';
    $html .= '<div class="progress-steps">';
    $html .= '<div class="step active" data-step="1">Preview</div>';
    $html .= '<div class="step" data-step="2">Customize</div>';
    $html .= '<div class="step" data-step="3">Generate</div>';
    $html .= '<div class="step" data-step="4">Export</div>';
    $html .= '</div>';
    $html .= '</div>';
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Build business plan template preview with charts and data visualizations
 */
function chatgabi_build_business_plan_preview($template, $sample_data) {
    $html = '<div class="template-content-preview business-plan-preview">';
    
    // Executive Summary Section
    $html .= '<section class="preview-section executive-summary">';
    $html .= '<h2 class="section-title">📋 Executive Summary</h2>';
    $html .= '<div class="section-content">';
    $html .= '<div class="business-overview-card">';
    $html .= '<h3>' . esc_html($sample_data['business_name']) . '</h3>';
    $html .= '<p class="business-tagline">' . esc_html($sample_data['business_description']) . '</p>';
    $html .= '<div class="key-metrics-grid">';
    $html .= '<div class="metric-item">';
    $html .= '<span class="metric-label">Target Market</span>';
    $html .= '<span class="metric-value">' . esc_html($sample_data['target_market']) . '</span>';
    $html .= '</div>';
    $html .= '<div class="metric-item">';
    $html .= '<span class="metric-label">Industry</span>';
    $html .= '<span class="metric-value">' . esc_html($sample_data['industry_sector']) . '</span>';
    $html .= '</div>';
    $html .= '<div class="metric-item">';
    $html .= '<span class="metric-label">Projected Revenue (Year 3)</span>';
    $html .= '<span class="metric-value">' . esc_html($sample_data['financial_projections']['year_3_revenue']) . '</span>';
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</section>';
    
    // Market Analysis with Charts
    $html .= '<section class="preview-section market-analysis">';
    $html .= '<h2 class="section-title">📊 Market Analysis</h2>';
    $html .= '<div class="section-content">';
    $html .= '<div class="chart-container">';
    $html .= '<canvas id="market-size-chart" class="preview-chart" data-chart-type="doughnut" data-chart-data="' . esc_attr(json_encode($sample_data['market_analysis']['market_segments'])) . '"></canvas>';
    $html .= '<div class="chart-caption">Market Size by Segment in ' . esc_html($sample_data['target_market']) . '</div>';
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</section>';
    
    // Financial Projections with Interactive Charts
    $html .= '<section class="preview-section financial-projections">';
    $html .= '<h2 class="section-title">💰 Financial Projections</h2>';
    $html .= '<div class="section-content">';
    $html .= '<div class="financial-charts-grid">';
    $html .= '<div class="chart-container">';
    $html .= '<canvas id="revenue-projection-chart" class="preview-chart" data-chart-type="line" data-chart-data="' . esc_attr(json_encode($sample_data['financial_projections']['revenue_timeline'])) . '"></canvas>';
    $html .= '<div class="chart-caption">3-Year Revenue Projection</div>';
    $html .= '</div>';
    $html .= '<div class="chart-container">';
    $html .= '<canvas id="expense-breakdown-chart" class="preview-chart" data-chart-type="bar" data-chart-data="' . esc_attr(json_encode($sample_data['financial_projections']['expense_breakdown'])) . '"></canvas>';
    $html .= '<div class="chart-caption">Expense Breakdown</div>';
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</div>';
    $html .= '</section>';
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Build interactive customization panel
 */
function chatgabi_build_preview_interactive_panel($template, $sample_data) {
    $html = '<div class="preview-interactive-panel">';
    $html .= '<div class="panel-header">';
    $html .= '<h3>🎨 Customize This Template</h3>';
    $html .= '<p>Personalize this template with your business information</p>';
    $html .= '</div>';
    
    $html .= '<div class="customization-form">';
    $html .= '<div class="form-section">';
    $html .= '<label for="preview-business-name">Business Name</label>';
    $html .= '<input type="text" id="preview-business-name" value="' . esc_attr($sample_data['business_name']) . '" class="preview-input" data-field="business_name" />';
    $html .= '</div>';
    
    $html .= '<div class="form-section">';
    $html .= '<label for="preview-industry">Industry</label>';
    $html .= '<select id="preview-industry" class="preview-select" data-field="industry">';
    $html .= '<option value="technology">Technology</option>';
    $html .= '<option value="agriculture">Agriculture</option>';
    $html .= '<option value="retail">Retail</option>';
    $html .= '<option value="healthcare">Healthcare</option>';
    $html .= '</select>';
    $html .= '</div>';
    
    $html .= '<div class="form-actions">';
    $html .= '<button class="btn-preview-update" onclick="updateTemplatePreview()">Update Preview</button>';
    $html .= '<button class="btn-ai-enhance" onclick="enhanceWithAI()">🤖 Enhance with AI</button>';
    $html .= '</div>';
    $html .= '</div>';
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Build preview footer with action buttons
 */
function chatgabi_build_preview_footer($template) {
    $html = '<div class="preview-footer-actions">';
    $html .= '<div class="action-buttons-grid">';
    $html .= '<button class="btn-primary btn-use-template" data-template-id="' . esc_attr($template->id) . '">';
    $html .= '🚀 Use This Template';
    $html .= '</button>';
    $html .= '<button class="btn-secondary btn-save-template" data-template-id="' . esc_attr($template->id) . '">';
    $html .= '💾 Save to My Templates';
    $html .= '</button>';
    $html .= '<button class="btn-outline btn-export-preview" data-template-id="' . esc_attr($template->id) . '">';
    $html .= '📄 Export Preview';
    $html .= '</button>';
    $html .= '</div>';
    $html .= '</div>';

    return $html;
}

/**
 * Generate sample business names based on industry and country
 */
function chatgabi_get_sample_business_name($industry, $country) {
    $business_names = array(
        'GH' => array(
            'technology' => array('GhanaCode Solutions', 'Accra Tech Hub', 'Digital Ghana Ventures'),
            'agriculture' => array('Golden Harvest Farms', 'Cocoa Excellence Ltd', 'Volta Valley Agro'),
            'retail' => array('Kumasi Market Express', 'Ghana Style Boutique', 'Tema Shopping Hub'),
            'healthcare' => array('Wellness Ghana Clinic', 'Ashanti Health Services', 'Cape Coast Medical')
        ),
        'KE' => array(
            'technology' => array('Nairobi Innovation Labs', 'Safari Tech Solutions', 'Kenya Digital Hub'),
            'agriculture' => array('Rift Valley Farms', 'Kenya Coffee Cooperative', 'Maasai Organic Produce'),
            'retail' => array('Mombasa Trade Center', 'Nairobi Fashion House', 'Kenya Craft Market'),
            'healthcare' => array('Nairobi Medical Center', 'Kenya Health Solutions', 'Mombasa Wellness Clinic')
        ),
        'NG' => array(
            'technology' => array('Lagos Tech Innovations', 'Abuja Digital Solutions', 'Nigeria Code Factory'),
            'agriculture' => array('Niger Delta Farms', 'Kano Agricultural Hub', 'Nigeria Grain Exchange'),
            'retail' => array('Lagos Shopping Plaza', 'Abuja Fashion Center', 'Nigeria Trade Hub'),
            'healthcare' => array('Lagos Medical Complex', 'Abuja Health Services', 'Nigeria Wellness Group')
        ),
        'ZA' => array(
            'technology' => array('Cape Town Tech Labs', 'Johannesburg Digital', 'South Africa Innovation'),
            'agriculture' => array('Western Cape Vineyards', 'Gauteng Agri Solutions', 'SA Organic Farms'),
            'retail' => array('Sandton Shopping Center', 'Cape Town Fashion Hub', 'SA Retail Network'),
            'healthcare' => array('Cape Town Medical Group', 'Johannesburg Health Hub', 'SA Wellness Centers')
        )
    );

    $country_names = $business_names[$country] ?? $business_names['GH'];
    $industry_names = $country_names[$industry] ?? $country_names['technology'];

    return $industry_names[array_rand($industry_names)];
}

/**
 * Generate sample business descriptions
 */
function chatgabi_get_sample_business_description($industry, $country) {
    $descriptions = array(
        'technology' => "Innovative technology solutions designed specifically for African markets, leveraging local insights and global best practices.",
        'agriculture' => "Sustainable agricultural enterprise focused on improving food security and farmer livelihoods across Africa.",
        'retail' => "Modern retail solutions connecting African consumers with quality products and services through innovative distribution channels.",
        'healthcare' => "Accessible healthcare services designed to address the unique health challenges and opportunities in African communities."
    );

    return $descriptions[$industry] ?? $descriptions['technology'];
}

/**
 * Generate sample financial data
 */
function chatgabi_get_sample_financial_data($country) {
    $currency_symbols = array(
        'GH' => 'GHS',
        'KE' => 'KES',
        'NG' => 'NGN',
        'ZA' => 'ZAR'
    );

    $currency = $currency_symbols[$country] ?? 'USD';

    return array(
        'year_1_revenue' => $currency . ' 150,000',
        'year_2_revenue' => $currency . ' 350,000',
        'year_3_revenue' => $currency . ' 750,000',
        'initial_investment' => $currency . ' 50,000',
        'break_even_month' => '18 months',
        'revenue_timeline' => array(
            'labels' => array('Year 1', 'Year 2', 'Year 3', 'Year 4', 'Year 5'),
            'data' => array(150000, 350000, 750000, 1200000, 1800000)
        ),
        'expense_breakdown' => array(
            'labels' => array('Operations', 'Marketing', 'Staff', 'Technology', 'Other'),
            'data' => array(40, 25, 20, 10, 5)
        )
    );
}

/**
 * Generate sample market analysis data
 */
function chatgabi_get_sample_market_data($industry, $country) {
    return array(
        'market_size' => 'USD 2.5 billion',
        'growth_rate' => '15% annually',
        'target_segments' => array('SMEs', 'Enterprises', 'Government'),
        'market_segments' => array(
            'labels' => array('SME Market', 'Enterprise', 'Government', 'Individual Consumers'),
            'data' => array(45, 30, 15, 10)
        )
    );
}

/**
 * Generate sample competitor data
 */
function chatgabi_get_sample_competitor_data($industry, $country) {
    return array(
        'main_competitors' => array('Competitor A', 'Competitor B', 'Competitor C'),
        'market_share' => array(
            'labels' => array('Our Company', 'Competitor A', 'Competitor B', 'Others'),
            'data' => array(25, 35, 20, 20)
        ),
        'competitive_advantages' => array(
            'Local market knowledge',
            'Innovative technology',
            'Strong partnerships',
            'Cost-effective solutions'
        )
    );
}

/**
 * Get template interactive elements
 */
function chatgabi_get_template_interactive_elements($template) {
    return array(
        'editable_fields' => array('business_name', 'industry', 'target_market', 'financial_goals'),
        'customizable_sections' => array('executive_summary', 'market_analysis', 'financial_projections'),
        'ai_enhancement_options' => array('improve_content', 'add_local_context', 'generate_variations'),
        'export_formats' => array('pdf', 'docx', 'html', 'presentation')
    );
}

/**
 * Get template customization options
 */
function chatgabi_get_template_customization_options($template) {
    return array(
        'themes' => array('professional', 'modern', 'creative', 'minimal'),
        'color_schemes' => array('blue', 'green', 'orange', 'purple'),
        'languages' => array('en', 'tw', 'sw', 'yo', 'zu'),
        'sections' => array(
            'executive_summary' => array('required' => true, 'customizable' => true),
            'market_analysis' => array('required' => true, 'customizable' => true),
            'financial_projections' => array('required' => false, 'customizable' => true),
            'appendices' => array('required' => false, 'customizable' => true)
        )
    );
}

/**
 * Estimate template completion time
 */
function chatgabi_estimate_template_completion_time($template) {
    $base_time = 30; // minutes
    $complexity_multiplier = 1;

    // Adjust based on template complexity
    if (strpos($template->prompt_text, 'financial') !== false) {
        $complexity_multiplier = 1.5;
    }
    if (strpos($template->prompt_text, 'market analysis') !== false) {
        $complexity_multiplier += 0.3;
    }

    $estimated_minutes = $base_time * $complexity_multiplier;

    return array(
        'minutes' => round($estimated_minutes),
        'display' => round($estimated_minutes) . ' minutes',
        'with_ai' => round($estimated_minutes * 0.6) . ' minutes with AI assistance'
    );
}
