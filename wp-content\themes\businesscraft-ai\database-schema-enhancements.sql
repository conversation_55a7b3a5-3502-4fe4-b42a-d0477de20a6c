-- ChatGABI Template System Database Schema Enhancements
-- Fixes critical issues and adds missing functionality

-- 1. Fix column name mismatch in prompt_templates table
ALTER TABLE wp_chatgabi_prompt_templates 
CHANGE COLUMN prompt_text prompt_content LONGTEXT NOT NULL;

-- 2. Add missing indexes for performance optimization
ALTER TABLE wp_chatgabi_prompt_templates 
ADD INDEX idx_template_search (title, is_public, status),
ADD INDEX idx_category_language (category_id, language_code),
ADD INDEX idx_usage_rating (usage_count, rating_average),
ADD INDEX idx_featured_public (is_featured, is_public, status);

-- 3. Add template complexity and metadata fields
ALTER TABLE wp_chatgabi_prompt_templates 
ADD COLUMN complexity_level ENUM('beginner', 'intermediate', 'advanced') DEFAULT 'intermediate',
ADD COLUMN estimated_completion_time INT DEFAULT 30 COMMENT 'Estimated completion time in minutes',
ADD COLUMN template_version VARCHAR(10) DEFAULT '1.0',
ADD COLUMN parent_template_id BIGINT(20) NULL,
ADD COLUMN template_metadata JSON NULL COMMENT 'Additional template configuration and settings',
ADD COLUMN preview_data JSON NULL COMMENT 'Cached preview data for performance';

-- 4. Create template usage analytics table
CREATE TABLE wp_chatgabi_template_analytics (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    template_id BIGINT(20) NOT NULL,
    user_id BIGINT(20) NOT NULL,
    action_type ENUM('view', 'preview', 'use', 'customize', 'export') NOT NULL,
    session_id VARCHAR(100),
    user_country VARCHAR(5),
    user_industry VARCHAR(100),
    completion_time INT NULL COMMENT 'Time spent in seconds',
    success_rate DECIMAL(5,2) NULL COMMENT 'Completion success rate',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_template_analytics (template_id, action_type, created_at),
    KEY idx_user_analytics (user_id, created_at),
    KEY idx_country_industry (user_country, user_industry, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 5. Create AI enhancement log table
CREATE TABLE wp_chatgabi_ai_enhancement_log (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    user_id BIGINT(20) NOT NULL,
    template_id BIGINT(20) NULL,
    field_id VARCHAR(100) NULL,
    enhancement_action VARCHAR(50) NOT NULL,
    credits_used DECIMAL(10,2) NOT NULL,
    tokens_used INT NULL,
    success TINYINT(1) DEFAULT 1,
    error_message TEXT NULL,
    processing_time DECIMAL(8,3) NULL COMMENT 'Processing time in seconds',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_user_enhancements (user_id, created_at),
    KEY idx_template_enhancements (template_id, enhancement_action),
    KEY idx_enhancement_analytics (enhancement_action, success, created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 6. Create template preview cache table for performance
CREATE TABLE wp_chatgabi_template_preview_cache (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    template_id BIGINT(20) NOT NULL,
    user_context_hash VARCHAR(64) NOT NULL COMMENT 'MD5 hash of user context for cache key',
    preview_html LONGTEXT NOT NULL,
    chart_data JSON NULL,
    sample_data JSON NULL,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    UNIQUE KEY idx_template_context (template_id, user_context_hash),
    KEY idx_cache_expiry (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 7. Add template collaboration features
CREATE TABLE wp_chatgabi_template_collaborations (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    template_id BIGINT(20) NOT NULL,
    owner_user_id BIGINT(20) NOT NULL,
    collaborator_user_id BIGINT(20) NOT NULL,
    permission_level ENUM('view', 'edit', 'admin') DEFAULT 'view',
    invitation_status ENUM('pending', 'accepted', 'declined') DEFAULT 'pending',
    invited_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    responded_at DATETIME NULL,
    PRIMARY KEY (id),
    UNIQUE KEY idx_template_collaborator (template_id, collaborator_user_id),
    KEY idx_owner_templates (owner_user_id, template_id),
    KEY idx_collaborator_invites (collaborator_user_id, invitation_status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 8. Create template comments and feedback table
CREATE TABLE wp_chatgabi_template_comments (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    template_id BIGINT(20) NOT NULL,
    user_id BIGINT(20) NOT NULL,
    parent_comment_id BIGINT(20) NULL,
    comment_text TEXT NOT NULL,
    comment_type ENUM('feedback', 'suggestion', 'question', 'improvement') DEFAULT 'feedback',
    is_public TINYINT(1) DEFAULT 0,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY idx_template_comments (template_id, status, created_at),
    KEY idx_user_comments (user_id, created_at),
    KEY idx_parent_comments (parent_comment_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 9. Add template export history
CREATE TABLE wp_chatgabi_template_exports (
    id BIGINT(20) NOT NULL AUTO_INCREMENT,
    template_id BIGINT(20) NOT NULL,
    user_id BIGINT(20) NOT NULL,
    export_format ENUM('pdf', 'docx', 'html', 'txt') NOT NULL,
    file_size INT NULL COMMENT 'File size in bytes',
    download_count INT DEFAULT 0,
    export_settings JSON NULL COMMENT 'Export configuration used',
    file_path VARCHAR(500) NULL COMMENT 'Path to exported file',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NULL COMMENT 'When the exported file expires',
    PRIMARY KEY (id),
    KEY idx_user_exports (user_id, created_at),
    KEY idx_template_exports (template_id, export_format),
    KEY idx_export_cleanup (expires_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 10. Update existing tables with foreign key constraints
ALTER TABLE wp_chatgabi_template_analytics 
ADD CONSTRAINT fk_analytics_template 
FOREIGN KEY (template_id) REFERENCES wp_chatgabi_prompt_templates(id) ON DELETE CASCADE;

ALTER TABLE wp_chatgabi_ai_enhancement_log 
ADD CONSTRAINT fk_enhancement_template 
FOREIGN KEY (template_id) REFERENCES wp_chatgabi_prompt_templates(id) ON DELETE SET NULL;

ALTER TABLE wp_chatgabi_template_preview_cache 
ADD CONSTRAINT fk_cache_template 
FOREIGN KEY (template_id) REFERENCES wp_chatgabi_prompt_templates(id) ON DELETE CASCADE;

ALTER TABLE wp_chatgabi_template_collaborations 
ADD CONSTRAINT fk_collaboration_template 
FOREIGN KEY (template_id) REFERENCES wp_chatgabi_prompt_templates(id) ON DELETE CASCADE;

ALTER TABLE wp_chatgabi_template_comments 
ADD CONSTRAINT fk_comments_template 
FOREIGN KEY (template_id) REFERENCES wp_chatgabi_prompt_templates(id) ON DELETE CASCADE;

ALTER TABLE wp_chatgabi_template_exports 
ADD CONSTRAINT fk_exports_template 
FOREIGN KEY (template_id) REFERENCES wp_chatgabi_prompt_templates(id) ON DELETE CASCADE;

-- 11. Create indexes for African market optimization
ALTER TABLE wp_chatgabi_prompt_templates 
ADD INDEX idx_african_context (country_code, sector, language_code),
ADD INDEX idx_template_performance (usage_count DESC, rating_average DESC, created_at DESC);

-- 12. Add template marketplace features
ALTER TABLE wp_chatgabi_prompt_templates 
ADD COLUMN is_premium TINYINT(1) DEFAULT 0,
ADD COLUMN price DECIMAL(10,2) NULL COMMENT 'Price in credits',
ADD COLUMN license_type ENUM('free', 'personal', 'commercial', 'enterprise') DEFAULT 'free',
ADD COLUMN download_count INT DEFAULT 0,
ADD COLUMN revenue_generated DECIMAL(15,2) DEFAULT 0.00;

-- 13. Performance optimization views
CREATE VIEW vw_chatgabi_template_stats AS
SELECT 
    t.id,
    t.title,
    t.category_id,
    t.usage_count,
    t.rating_average,
    t.rating_count,
    t.download_count,
    COUNT(DISTINCT a.user_id) as unique_users,
    AVG(a.completion_time) as avg_completion_time,
    SUM(CASE WHEN a.action_type = 'export' THEN 1 ELSE 0 END) as export_count,
    t.created_at
FROM wp_chatgabi_prompt_templates t
LEFT JOIN wp_chatgabi_template_analytics a ON t.id = a.template_id
WHERE t.status = 'active'
GROUP BY t.id;

-- 14. Add data retention policies
CREATE EVENT IF NOT EXISTS cleanup_expired_preview_cache
ON SCHEDULE EVERY 1 DAY
DO
DELETE FROM wp_chatgabi_template_preview_cache 
WHERE expires_at < NOW();

CREATE EVENT IF NOT EXISTS cleanup_old_analytics
ON SCHEDULE EVERY 1 MONTH
DO
DELETE FROM wp_chatgabi_template_analytics 
WHERE created_at < DATE_SUB(NOW(), INTERVAL 2 YEAR);

-- 15. Insert sample enhanced templates with new fields
INSERT INTO wp_chatgabi_prompt_templates (
    user_id, title, description, prompt_content, category_id, 
    complexity_level, estimated_completion_time, template_version,
    is_public, is_featured, language_code, country_code, sector
) VALUES 
(0, 'AI-Enhanced Business Plan for African Startups', 
 'Comprehensive business plan template with AI-powered market analysis and financial projections', 
 'Create a detailed business plan for {business_name} in the {industry} sector targeting {country} market...', 
 1, 'intermediate', 45, '2.0', 1, 1, 'en', '', 'technology'),
(0, 'Market Entry Strategy for African Markets', 
 'Strategic framework for entering new African markets with cultural considerations', 
 'Develop a market entry strategy for {business_name} entering the {country} market...', 
 4, 'advanced', 60, '2.0', 1, 1, 'en', '', 'general');

-- 16. Update template metadata with enhanced features
UPDATE wp_chatgabi_prompt_templates 
SET template_metadata = JSON_OBJECT(
    'ai_enhanced', true,
    'supports_charts', true,
    'interactive_preview', true,
    'african_context', true,
    'collaboration_enabled', true
)
WHERE is_featured = 1;
