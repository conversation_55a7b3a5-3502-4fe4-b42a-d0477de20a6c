/**
 * Enhanced Template Preview System JavaScript
 * Interactive preview with charts, customization, and AI integration
 */

class ChatGABITemplatePreview {
    constructor() {
        this.currentTemplate = null;
        this.charts = {};
        this.sampleData = {};
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeCharts();
        this.setupInteractiveElements();
    }

    bindEvents() {
        // Preview update events
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-preview-update')) {
                this.updateTemplatePreview();
            }
            if (e.target.classList.contains('btn-ai-enhance')) {
                this.enhanceWithAI();
            }
            if (e.target.classList.contains('btn-use-template')) {
                this.useTemplate(e.target.dataset.templateId);
            }
            if (e.target.classList.contains('btn-save-template')) {
                this.saveTemplate(e.target.dataset.templateId);
            }
            if (e.target.classList.contains('btn-export-preview')) {
                this.exportPreview(e.target.dataset.templateId);
            }
        });

        // Input change events for real-time preview updates
        document.addEventListener('input', (e) => {
            if (e.target.classList.contains('preview-input') || e.target.classList.contains('preview-select')) {
                this.debounce(() => this.updatePreviewField(e.target), 500)();
            }
        });

        // Progress step navigation
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('step')) {
                this.navigateToStep(e.target.dataset.step);
            }
        });
    }

    /**
     * Initialize Chart.js charts for data visualization
     */
    initializeCharts() {
        // Wait for Chart.js to load
        if (typeof Chart === 'undefined') {
            setTimeout(() => this.initializeCharts(), 100);
            return;
        }

        const chartElements = document.querySelectorAll('.preview-chart');
        chartElements.forEach(canvas => {
            const chartType = canvas.dataset.chartType;
            const chartData = JSON.parse(canvas.dataset.chartData || '{}');
            
            this.createChart(canvas, chartType, chartData);
        });
    }

    /**
     * Create individual chart
     */
    createChart(canvas, type, data) {
        const ctx = canvas.getContext('2d');
        const chartId = canvas.id;

        let chartConfig = {
            type: type,
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        };

        // Customize based on chart type
        switch (type) {
            case 'doughnut':
                chartConfig.options.cutout = '60%';
                chartConfig.data.datasets = [{
                    data: data.data,
                    backgroundColor: [
                        '#007cba', '#28a745', '#ffc107', '#dc3545', '#6c757d'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }];
                break;
            
            case 'line':
                chartConfig.options.scales = {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                };
                chartConfig.data.datasets = [{
                    label: 'Revenue Projection',
                    data: data.data,
                    borderColor: '#007cba',
                    backgroundColor: 'rgba(0, 124, 186, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }];
                break;
            
            case 'bar':
                chartConfig.options.scales = {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                };
                chartConfig.data.datasets = [{
                    label: 'Expense Breakdown',
                    data: data.data,
                    backgroundColor: [
                        '#007cba', '#28a745', '#ffc107', '#dc3545', '#6c757d'
                    ],
                    borderWidth: 1
                }];
                break;
        }

        this.charts[chartId] = new Chart(ctx, chartConfig);
    }

    /**
     * Update template preview with new data
     */
    updateTemplatePreview() {
        const formData = this.collectFormData();
        
        // Show loading state
        this.showLoadingState('Updating preview...');
        
        // Make AJAX request to update preview
        fetch(chatgabiTemplatesConfig.restUrl + 'templates/preview/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': chatgabiTemplatesConfig.restNonce
            },
            body: JSON.stringify({
                template_id: this.currentTemplate,
                form_data: formData
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.updatePreviewContent(data.preview_html);
                this.updateCharts(data.chart_data);
                this.showMessage('success', 'Preview updated successfully!');
            } else {
                this.showMessage('error', data.message || 'Failed to update preview');
            }
        })
        .catch(error => {
            console.error('Preview update error:', error);
            this.showMessage('error', 'An error occurred while updating the preview');
        })
        .finally(() => {
            this.hideLoadingState();
        });
    }

    /**
     * Enhance template with AI
     */
    enhanceWithAI() {
        const formData = this.collectFormData();
        
        // Check credits
        if (chatgabiTemplatesConfig.userCredits < 2) {
            this.showMessage('error', 'Insufficient credits for AI enhancement. Required: 2 credits');
            return;
        }
        
        this.showLoadingState('AI is enhancing your template...');
        
        fetch(chatgabiTemplatesConfig.restUrl + 'templates/enhance', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': chatgabiTemplatesConfig.restNonce
            },
            body: JSON.stringify({
                template_id: this.currentTemplate,
                user_context: formData,
                enhancement_options: {
                    improve_content: true,
                    add_local_context: true,
                    generate_examples: true
                }
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.updatePreviewContent(data.enhanced_preview);
                this.updateCreditsDisplay(data.remaining_credits);
                this.showMessage('success', 'Template enhanced with AI successfully!');
            } else {
                this.showMessage('error', data.message || 'AI enhancement failed');
            }
        })
        .catch(error => {
            console.error('AI enhancement error:', error);
            this.showMessage('error', 'An error occurred during AI enhancement');
        })
        .finally(() => {
            this.hideLoadingState();
        });
    }

    /**
     * Use template - redirect to generation interface
     */
    useTemplate(templateId) {
        const formData = this.collectFormData();
        
        // Store customization data in session
        sessionStorage.setItem('template_customization', JSON.stringify(formData));
        
        // Redirect to template generation page
        window.location.href = chatgabiTemplatesConfig.templateGeneratorUrl + '&template_id=' + templateId;
    }

    /**
     * Save template to user's collection
     */
    saveTemplate(templateId) {
        this.showLoadingState('Saving template...');
        
        fetch(chatgabiTemplatesConfig.restUrl + 'templates/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': chatgabiTemplatesConfig.restNonce
            },
            body: JSON.stringify({
                template_id: templateId,
                customizations: this.collectFormData()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showMessage('success', 'Template saved to your collection!');
            } else {
                this.showMessage('error', data.message || 'Failed to save template');
            }
        })
        .catch(error => {
            console.error('Save template error:', error);
            this.showMessage('error', 'An error occurred while saving the template');
        })
        .finally(() => {
            this.hideLoadingState();
        });
    }

    /**
     * Export preview as PDF/DOCX
     */
    exportPreview(templateId) {
        this.showLoadingState('Generating export...');
        
        const formData = this.collectFormData();
        
        fetch(chatgabiTemplatesConfig.restUrl + 'templates/export', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': chatgabiTemplatesConfig.restNonce
            },
            body: JSON.stringify({
                template_id: templateId,
                export_format: 'pdf',
                customizations: formData
            })
        })
        .then(response => response.blob())
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `template-preview-${templateId}.pdf`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            this.showMessage('success', 'Template exported successfully!');
        })
        .catch(error => {
            console.error('Export error:', error);
            this.showMessage('error', 'An error occurred during export');
        })
        .finally(() => {
            this.hideLoadingState();
        });
    }

    /**
     * Collect form data from customization inputs
     */
    collectFormData() {
        const formData = {};
        
        document.querySelectorAll('.preview-input, .preview-select').forEach(input => {
            const field = input.dataset.field;
            if (field) {
                formData[field] = input.value;
            }
        });
        
        return formData;
    }

    /**
     * Update preview content
     */
    updatePreviewContent(html) {
        const previewContainer = document.querySelector('.template-content-preview');
        if (previewContainer) {
            previewContainer.innerHTML = html;
            this.initializeCharts(); // Reinitialize charts after content update
        }
    }

    /**
     * Update charts with new data
     */
    updateCharts(chartData) {
        Object.keys(chartData).forEach(chartId => {
            if (this.charts[chartId]) {
                this.charts[chartId].data = chartData[chartId];
                this.charts[chartId].update();
            }
        });
    }

    /**
     * Utility functions
     */
    showLoadingState(message) {
        // Create or show loading overlay
        let loadingOverlay = document.querySelector('.preview-loading-overlay');
        if (!loadingOverlay) {
            loadingOverlay = document.createElement('div');
            loadingOverlay.className = 'preview-loading-overlay';
            loadingOverlay.innerHTML = `
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <div class="loading-message">${message}</div>
                </div>
            `;
            loadingOverlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
            `;
            document.body.appendChild(loadingOverlay);
        } else {
            loadingOverlay.querySelector('.loading-message').textContent = message;
            loadingOverlay.style.display = 'flex';
        }
    }

    hideLoadingState() {
        const loadingOverlay = document.querySelector('.preview-loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    showMessage(type, message) {
        // Create message element
        const messageEl = document.createElement('div');
        messageEl.className = `preview-message preview-message-${type}`;
        messageEl.innerHTML = `
            <span class="message-icon">${type === 'success' ? '✅' : '❌'}</span>
            <span class="message-text">${message}</span>
            <button class="message-close" onclick="this.parentElement.remove()">×</button>
        `;

        // Style the message
        messageEl.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#d4edda' : '#f8d7da'};
            color: ${type === 'success' ? '#155724' : '#721c24'};
            border: 1px solid ${type === 'success' ? '#c3e6cb' : '#f5c6cb'};
            border-radius: 8px;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 10000;
            max-width: 400px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        `;

        // Add to page
        document.body.appendChild(messageEl);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (messageEl.parentElement) {
                messageEl.remove();
            }
        }, 5000);
    }

    updateCreditsDisplay(credits) {
        const creditsDisplay = document.querySelector('.credits-amount');
        if (creditsDisplay) {
            creditsDisplay.textContent = credits.toFixed(2);
        }

        // Update global config
        if (window.chatgabiTemplatesConfig) {
            window.chatgabiTemplatesConfig.userCredits = credits;
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    setupInteractiveElements() {
        // Setup real-time preview updates
        const previewInputs = document.querySelectorAll('.preview-input, .preview-select');
        previewInputs.forEach(input => {
            input.addEventListener('input', () => {
                this.updatePreviewField(input);
            });
        });

        // Setup template theme switching
        const themeButtons = document.querySelectorAll('.theme-option');
        themeButtons.forEach(button => {
            button.addEventListener('click', () => {
                this.switchTheme(button.dataset.theme);
            });
        });

        // Setup responsive chart resizing
        window.addEventListener('resize', () => {
            Object.values(this.charts).forEach(chart => {
                chart.resize();
            });
        });
    }

    navigateToStep(step) {
        // Handle step navigation
        document.querySelectorAll('.step').forEach(s => s.classList.remove('active'));
        const targetStep = document.querySelector(`[data-step="${step}"]`);
        if (targetStep) {
            targetStep.classList.add('active');
        }

        // Show/hide relevant sections based on step
        this.showStepContent(step);
    }

    showStepContent(step) {
        // Hide all step content
        document.querySelectorAll('.step-content').forEach(content => {
            content.style.display = 'none';
        });

        // Show current step content
        const currentContent = document.querySelector(`.step-content[data-step="${step}"]`);
        if (currentContent) {
            currentContent.style.display = 'block';
        }
    }

    updatePreviewField(input) {
        const field = input.dataset.field;
        const value = input.value;

        // Update corresponding preview elements
        const previewElements = document.querySelectorAll(`[data-preview-field="${field}"]`);
        previewElements.forEach(element => {
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                element.value = value;
            } else {
                element.textContent = value;
            }
        });

        // Update charts if the field affects chart data
        if (field === 'business_name') {
            this.updateChartTitles(value);
        }
    }

    updateChartTitles(businessName) {
        Object.values(this.charts).forEach(chart => {
            if (chart.options.plugins && chart.options.plugins.title) {
                chart.options.plugins.title.text = chart.options.plugins.title.text.replace(/\{business_name\}/g, businessName);
                chart.update();
            }
        });
    }

    switchTheme(theme) {
        const previewContainer = document.querySelector('.chatgabi-template-preview-enhanced');
        if (previewContainer) {
            // Remove existing theme classes
            previewContainer.classList.remove('theme-professional', 'theme-modern', 'theme-creative', 'theme-minimal');
            // Add new theme class
            previewContainer.classList.add(`theme-${theme}`);
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.chatgabi-template-preview-enhanced')) {
        window.chatgabiTemplatePreview = new ChatGABITemplatePreview();
    }
});

// Global functions for inline event handlers
window.updateTemplatePreview = function() {
    if (window.chatgabiTemplatePreview) {
        window.chatgabiTemplatePreview.updateTemplatePreview();
    }
};

window.enhanceWithAI = function() {
    if (window.chatgabiTemplatePreview) {
        window.chatgabiTemplatePreview.enhanceWithAI();
    }
};
