/**
 * Enhanced Template Preview System JavaScript
 * Interactive preview with charts, customization, and AI integration
 */

class ChatGABITemplatePreview {
    constructor() {
        this.currentTemplate = null;
        this.charts = {};
        this.sampleData = {};
        this.init();
    }

    init() {
        this.bindEvents();
        this.initializeCharts();
        this.setupInteractiveElements();
    }

    bindEvents() {
        // Preview update events
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('btn-preview-update')) {
                this.updateTemplatePreview();
            }
            if (e.target.classList.contains('btn-ai-enhance')) {
                this.enhanceWithAI();
            }
            if (e.target.classList.contains('btn-use-template')) {
                this.useTemplate(e.target.dataset.templateId);
            }
            if (e.target.classList.contains('btn-save-template')) {
                this.saveTemplate(e.target.dataset.templateId);
            }
            if (e.target.classList.contains('btn-export-preview')) {
                this.exportPreview(e.target.dataset.templateId);
            }
        });

        // Input change events for real-time preview updates
        document.addEventListener('input', (e) => {
            if (e.target.classList.contains('preview-input') || e.target.classList.contains('preview-select')) {
                this.debounce(() => this.updatePreviewField(e.target), 500)();
            }
        });

        // Progress step navigation
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('step')) {
                this.navigateToStep(e.target.dataset.step);
            }
        });
    }

    /**
     * Initialize Chart.js charts for data visualization
     */
    initializeCharts() {
        // Wait for Chart.js to load
        if (typeof Chart === 'undefined') {
            setTimeout(() => this.initializeCharts(), 100);
            return;
        }

        const chartElements = document.querySelectorAll('.preview-chart');
        chartElements.forEach(canvas => {
            const chartType = canvas.dataset.chartType;
            const chartData = JSON.parse(canvas.dataset.chartData || '{}');
            
            this.createChart(canvas, chartType, chartData);
        });
    }

    /**
     * Create individual chart
     */
    createChart(canvas, type, data) {
        const ctx = canvas.getContext('2d');
        const chartId = canvas.id;

        let chartConfig = {
            type: type,
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        };

        // Customize based on chart type
        switch (type) {
            case 'doughnut':
                chartConfig.options.cutout = '60%';
                chartConfig.data.datasets = [{
                    data: data.data,
                    backgroundColor: [
                        '#007cba', '#28a745', '#ffc107', '#dc3545', '#6c757d'
                    ],
                    borderWidth: 2,
                    borderColor: '#ffffff'
                }];
                break;
            
            case 'line':
                chartConfig.options.scales = {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            }
                        }
                    }
                };
                chartConfig.data.datasets = [{
                    label: 'Revenue Projection',
                    data: data.data,
                    borderColor: '#007cba',
                    backgroundColor: 'rgba(0, 124, 186, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }];
                break;
            
            case 'bar':
                chartConfig.options.scales = {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                };
                chartConfig.data.datasets = [{
                    label: 'Expense Breakdown',
                    data: data.data,
                    backgroundColor: [
                        '#007cba', '#28a745', '#ffc107', '#dc3545', '#6c757d'
                    ],
                    borderWidth: 1
                }];
                break;
        }

        this.charts[chartId] = new Chart(ctx, chartConfig);
    }

    /**
     * Update template preview with new data
     */
    updateTemplatePreview() {
        const formData = this.collectFormData();
        
        // Show loading state
        this.showLoadingState('Updating preview...');
        
        // Make AJAX request to update preview
        fetch(chatgabiTemplatesConfig.restUrl + 'templates/preview/update', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': chatgabiTemplatesConfig.restNonce
            },
            body: JSON.stringify({
                template_id: this.currentTemplate,
                form_data: formData
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.updatePreviewContent(data.preview_html);
                this.updateCharts(data.chart_data);
                this.showMessage('success', 'Preview updated successfully!');
            } else {
                this.showMessage('error', data.message || 'Failed to update preview');
            }
        })
        .catch(error => {
            console.error('Preview update error:', error);
            this.showMessage('error', 'An error occurred while updating the preview');
        })
        .finally(() => {
            this.hideLoadingState();
        });
    }

    /**
     * Enhance template with AI
     */
    enhanceWithAI() {
        const formData = this.collectFormData();
        
        // Check credits
        if (chatgabiTemplatesConfig.userCredits < 2) {
            this.showMessage('error', 'Insufficient credits for AI enhancement. Required: 2 credits');
            return;
        }
        
        this.showLoadingState('AI is enhancing your template...');
        
        fetch(chatgabiTemplatesConfig.restUrl + 'templates/enhance', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': chatgabiTemplatesConfig.restNonce
            },
            body: JSON.stringify({
                template_id: this.currentTemplate,
                user_context: formData,
                enhancement_options: {
                    improve_content: true,
                    add_local_context: true,
                    generate_examples: true
                }
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.updatePreviewContent(data.enhanced_preview);
                this.updateCreditsDisplay(data.remaining_credits);
                this.showMessage('success', 'Template enhanced with AI successfully!');
            } else {
                this.showMessage('error', data.message || 'AI enhancement failed');
            }
        })
        .catch(error => {
            console.error('AI enhancement error:', error);
            this.showMessage('error', 'An error occurred during AI enhancement');
        })
        .finally(() => {
            this.hideLoadingState();
        });
    }

    /**
     * Use template - redirect to generation interface
     */
    useTemplate(templateId) {
        const formData = this.collectFormData();
        
        // Store customization data in session
        sessionStorage.setItem('template_customization', JSON.stringify(formData));
        
        // Redirect to template generation page
        window.location.href = chatgabiTemplatesConfig.templateGeneratorUrl + '&template_id=' + templateId;
    }

    /**
     * Save template to user's collection
     */
    saveTemplate(templateId) {
        this.showLoadingState('Saving template...');
        
        fetch(chatgabiTemplatesConfig.restUrl + 'templates/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': chatgabiTemplatesConfig.restNonce
            },
            body: JSON.stringify({
                template_id: templateId,
                customizations: this.collectFormData()
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.showMessage('success', 'Template saved to your collection!');
            } else {
                this.showMessage('error', data.message || 'Failed to save template');
            }
        })
        .catch(error => {
            console.error('Save template error:', error);
            this.showMessage('error', 'An error occurred while saving the template');
        })
        .finally(() => {
            this.hideLoadingState();
        });
    }

    /**
     * Export preview as PDF/DOCX
     */
    exportPreview(templateId) {
        this.showLoadingState('Generating export...');
        
        const formData = this.collectFormData();
        
        fetch(chatgabiTemplatesConfig.restUrl + 'templates/export', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': chatgabiTemplatesConfig.restNonce
            },
            body: JSON.stringify({
                template_id: templateId,
                export_format: 'pdf',
                customizations: formData
            })
        })
        .then(response => response.blob())
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `template-preview-${templateId}.pdf`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
            this.showMessage('success', 'Template exported successfully!');
        })
        .catch(error => {
            console.error('Export error:', error);
            this.showMessage('error', 'An error occurred during export');
        })
        .finally(() => {
            this.hideLoadingState();
        });
    }

    /**
     * Collect form data from customization inputs
     */
    collectFormData() {
        const formData = {};
        
        document.querySelectorAll('.preview-input, .preview-select').forEach(input => {
            const field = input.dataset.field;
            if (field) {
                formData[field] = input.value;
            }
        });
        
        return formData;
    }

    /**
     * Update preview content
     */
    updatePreviewContent(html) {
        const previewContainer = document.querySelector('.template-content-preview');
        if (previewContainer) {
            previewContainer.innerHTML = html;
            this.initializeCharts(); // Reinitialize charts after content update
        }
    }

    /**
     * Update charts with new data
     */
    updateCharts(chartData) {
        Object.keys(chartData).forEach(chartId => {
            if (this.charts[chartId]) {
                this.charts[chartId].data = chartData[chartId];
                this.charts[chartId].update();
            }
        });
    }

    /**
     * Utility functions
     */
    showLoadingState(message) {
        // Implementation for loading state
        console.log('Loading:', message);
    }

    hideLoadingState() {
        // Implementation for hiding loading state
        console.log('Loading complete');
    }

    showMessage(type, message) {
        // Implementation for showing messages
        console.log(`${type}: ${message}`);
    }

    updateCreditsDisplay(credits) {
        const creditsDisplay = document.querySelector('.credits-amount');
        if (creditsDisplay) {
            creditsDisplay.textContent = credits.toFixed(2);
        }
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    setupInteractiveElements() {
        // Setup any additional interactive elements
        console.log('Interactive elements initialized');
    }

    navigateToStep(step) {
        // Handle step navigation
        document.querySelectorAll('.step').forEach(s => s.classList.remove('active'));
        document.querySelector(`[data-step="${step}"]`).classList.add('active');
    }

    updatePreviewField(input) {
        // Handle individual field updates
        const field = input.dataset.field;
        const value = input.value;
        
        // Update corresponding preview elements
        const previewElements = document.querySelectorAll(`[data-preview-field="${field}"]`);
        previewElements.forEach(element => {
            element.textContent = value;
        });
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('.chatgabi-template-preview-enhanced')) {
        window.chatgabiTemplatePreview = new ChatGABITemplatePreview();
    }
});

// Global functions for inline event handlers
window.updateTemplatePreview = function() {
    if (window.chatgabiTemplatePreview) {
        window.chatgabiTemplatePreview.updateTemplatePreview();
    }
};

window.enhanceWithAI = function() {
    if (window.chatgabiTemplatePreview) {
        window.chatgabiTemplatePreview.enhanceWithAI();
    }
};
