/**
 * Enhanced Template Preview System Styles
 * WordPress Theme-Style Preview Interface
 */

/* Enhanced Preview Container */
.chatgabi-template-preview-enhanced {
    max-width: 1200px;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Enhanced Preview Header */
.preview-header-enhanced {
    background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
    color: white;
    padding: 30px;
    position: relative;
}

.template-branding {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 25px;
}

.preview-logo {
    height: 50px;
    width: auto;
}

.template-meta-info h1 {
    font-size: 2.2rem;
    margin: 0 0 10px 0;
    font-weight: 700;
}

.template-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 15px;
    line-height: 1.5;
}

.template-context-tags {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.context-tag {
    background: rgba(255, 255, 255, 0.2);
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

.country-tag {
    background: rgba(46, 204, 113, 0.3);
}

.industry-tag {
    background: rgba(52, 152, 219, 0.3);
}

.language-tag {
    background: rgba(155, 89, 182, 0.3);
}

/* Progress Indicator */
.template-progress-indicator {
    margin-top: 20px;
}

.progress-steps {
    display: flex;
    justify-content: center;
    gap: 20px;
}

.step {
    padding: 8px 16px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.step.active {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

/* Template Content Preview */
.template-content-preview {
    padding: 40px;
}

.preview-section {
    margin-bottom: 40px;
    border: 1px solid #e1e5e9;
    border-radius: 12px;
    overflow: hidden;
}

.section-title {
    background: #f8f9fa;
    padding: 20px 30px;
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid #e1e5e9;
}

.section-content {
    padding: 30px;
}

/* Business Overview Card */
.business-overview-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 25px;
    border-radius: 10px;
    text-align: center;
}

.business-overview-card h3 {
    font-size: 1.8rem;
    color: #007cba;
    margin: 0 0 10px 0;
    font-weight: 700;
}

.business-tagline {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
}

/* Key Metrics Grid */
.key-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.metric-item {
    background: white;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.metric-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
}

.metric-value {
    display: block;
    font-size: 1.3rem;
    color: #007cba;
    font-weight: 700;
}

/* Charts and Visualizations */
.chart-container {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    text-align: center;
}

.preview-chart {
    max-width: 100%;
    height: 300px;
}

.chart-caption {
    margin-top: 15px;
    font-size: 0.9rem;
    color: #666;
    font-weight: 500;
}

.financial-charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 30px;
}

/* Interactive Customization Panel */
.preview-interactive-panel {
    background: #f8f9fa;
    border-top: 3px solid #007cba;
    padding: 30px;
    margin: 40px 0;
}

.panel-header {
    text-align: center;
    margin-bottom: 30px;
}

.panel-header h3 {
    font-size: 1.5rem;
    color: #333;
    margin: 0 0 10px 0;
}

.panel-header p {
    color: #666;
    font-size: 1rem;
}

.customization-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-section {
    margin-bottom: 20px;
}

.form-section label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.preview-input,
.preview-select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 16px;
    transition: border-color 0.3s ease;
}

.preview-input:focus,
.preview-select:focus {
    outline: none;
    border-color: #007cba;
    box-shadow: 0 0 0 3px rgba(0, 124, 186, 0.1);
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.btn-preview-update,
.btn-ai-enhance {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-preview-update {
    background: #007cba;
    color: white;
}

.btn-preview-update:hover {
    background: #005a87;
    transform: translateY(-1px);
}

.btn-ai-enhance {
    background: #28a745;
    color: white;
}

.btn-ai-enhance:hover {
    background: #218838;
    transform: translateY(-1px);
}

/* Footer Actions */
.preview-footer-actions {
    background: #f8f9fa;
    padding: 30px;
    border-top: 1px solid #e1e5e9;
}

.action-buttons-grid {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-primary,
.btn-secondary,
.btn-outline {
    padding: 15px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: #007cba;
    color: white;
}

.btn-primary:hover {
    background: #005a87;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 124, 186, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    color: #007cba;
    border: 2px solid #007cba;
}

.btn-outline:hover {
    background: #007cba;
    color: white;
    transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .template-branding {
        flex-direction: column;
        text-align: center;
    }
    
    .key-metrics-grid {
        grid-template-columns: 1fr;
    }
    
    .financial-charts-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons-grid {
        flex-direction: column;
        align-items: center;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .progress-steps {
        flex-wrap: wrap;
        gap: 10px;
    }
}
