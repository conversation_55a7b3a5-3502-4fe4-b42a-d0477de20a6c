<?php
/**
 * AI-Assisted Context-Aware Template Widgets
 * Embedded AI assistance for template fields and customization
 * 
 * @package ChatGABI
 * @since 2.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Generate AI assistance widget for template fields
 */
function chatgabi_generate_ai_widget($field_type, $field_context, $user_context = array()) {
    $widget_id = 'ai-widget-' . uniqid();
    
    $html = '<div class="chatgabi-ai-widget" data-widget-id="' . esc_attr($widget_id) . '" data-field-type="' . esc_attr($field_type) . '">';
    
    // Widget header
    $html .= '<div class="ai-widget-header">';
    $html .= '<span class="ai-widget-icon">🤖</span>';
    $html .= '<span class="ai-widget-title">' . __('AI Assistant', 'chatgabi') . '</span>';
    $html .= '<button class="ai-widget-toggle" onclick="toggleAIWidget(\'' . $widget_id . '\')">';
    $html .= '<span class="toggle-icon">▼</span>';
    $html .= '</button>';
    $html .= '</div>';
    
    // Widget content
    $html .= '<div class="ai-widget-content" id="' . esc_attr($widget_id) . '-content">';
    
    // Field-specific AI suggestions
    $html .= chatgabi_generate_field_suggestions($field_type, $field_context, $user_context);
    
    // AI enhancement options
    $html .= '<div class="ai-enhancement-options">';
    $html .= '<h4>' . __('AI Enhancement Options', 'chatgabi') . '</h4>';
    $html .= '<div class="enhancement-buttons">';
    
    switch ($field_type) {
        case 'business_description':
            $html .= '<button class="ai-enhance-btn" data-action="improve_description" data-field="' . esc_attr($field_context['field_id']) . '">';
            $html .= '✨ ' . __('Improve Description', 'chatgabi');
            $html .= '</button>';
            $html .= '<button class="ai-enhance-btn" data-action="add_local_context" data-field="' . esc_attr($field_context['field_id']) . '">';
            $html .= '🌍 ' . __('Add Local Context', 'chatgabi');
            $html .= '</button>';
            break;
            
        case 'market_analysis':
            $html .= '<button class="ai-enhance-btn" data-action="generate_market_data" data-field="' . esc_attr($field_context['field_id']) . '">';
            $html .= '📊 ' . __('Generate Market Data', 'chatgabi');
            $html .= '</button>';
            $html .= '<button class="ai-enhance-btn" data-action="competitor_analysis" data-field="' . esc_attr($field_context['field_id']) . '">';
            $html .= '🏢 ' . __('Competitor Analysis', 'chatgabi');
            $html .= '</button>';
            break;
            
        case 'financial_projections':
            $html .= '<button class="ai-enhance-btn" data-action="generate_projections" data-field="' . esc_attr($field_context['field_id']) . '">';
            $html .= '💰 ' . __('Generate Projections', 'chatgabi');
            $html .= '</button>';
            $html .= '<button class="ai-enhance-btn" data-action="validate_assumptions" data-field="' . esc_attr($field_context['field_id']) . '">';
            $html .= '✅ ' . __('Validate Assumptions', 'chatgabi');
            $html .= '</button>';
            break;
            
        default:
            $html .= '<button class="ai-enhance-btn" data-action="improve_content" data-field="' . esc_attr($field_context['field_id']) . '">';
            $html .= '🚀 ' . __('Enhance Content', 'chatgabi');
            $html .= '</button>';
    }
    
    $html .= '</div>';
    $html .= '</div>';
    
    // AI suggestions display
    $html .= '<div class="ai-suggestions-display" id="' . esc_attr($widget_id) . '-suggestions">';
    $html .= '<div class="suggestions-placeholder">';
    $html .= '<p>' . __('Click an enhancement button to get AI-powered suggestions for this field.', 'chatgabi') . '</p>';
    $html .= '</div>';
    $html .= '</div>';
    
    // Credit cost display
    $html .= '<div class="ai-widget-footer">';
    $html .= '<div class="credit-cost-info">';
    $html .= '<span class="cost-label">' . __('Enhancement Cost:', 'chatgabi') . '</span>';
    $html .= '<span class="cost-amount">1-2 ' . __('credits', 'chatgabi') . '</span>';
    $html .= '</div>';
    $html .= '</div>';
    
    $html .= '</div>'; // Close ai-widget-content
    $html .= '</div>'; // Close chatgabi-ai-widget
    
    return $html;
}

/**
 * Generate field-specific AI suggestions
 */
function chatgabi_generate_field_suggestions($field_type, $field_context, $user_context) {
    $html = '<div class="field-suggestions">';
    $html .= '<h4>' . __('Smart Suggestions', 'chatgabi') . '</h4>';
    
    $suggestions = chatgabi_get_field_suggestions($field_type, $user_context);
    
    if (!empty($suggestions)) {
        $html .= '<div class="suggestions-list">';
        foreach ($suggestions as $suggestion) {
            $html .= '<div class="suggestion-item" onclick="applySuggestion(\'' . esc_js($suggestion['text']) . '\', \'' . esc_attr($field_context['field_id']) . '\')">';
            $html .= '<div class="suggestion-text">' . esc_html($suggestion['text']) . '</div>';
            $html .= '<div class="suggestion-meta">';
            $html .= '<span class="suggestion-type">' . esc_html($suggestion['type']) . '</span>';
            if (isset($suggestion['confidence'])) {
                $html .= '<span class="suggestion-confidence">' . esc_html($suggestion['confidence']) . '% match</span>';
            }
            $html .= '</div>';
            $html .= '</div>';
        }
        $html .= '</div>';
    } else {
        $html .= '<p class="no-suggestions">' . __('No suggestions available for this field type.', 'chatgabi') . '</p>';
    }
    
    $html .= '</div>';
    
    return $html;
}

/**
 * Get AI-powered field suggestions based on user context
 */
function chatgabi_get_field_suggestions($field_type, $user_context) {
    $user_country = $user_context['country'] ?? 'GH';
    $user_industry = $user_context['industry'] ?? 'general';
    
    // Get African Context Engine suggestions
    $suggestions = array();
    
    if (class_exists('BusinessCraft_African_Context_Engine')) {
        $african_context = new BusinessCraft_African_Context_Engine();
        $market_examples = $african_context->generate_market_examples($user_country, $user_industry);
        
        switch ($field_type) {
            case 'business_description':
                $suggestions = array(
                    array(
                        'text' => 'Innovative ' . $user_industry . ' solution designed for the ' . chatgabi_get_country_name_from_code($user_country) . ' market',
                        'type' => 'Template',
                        'confidence' => 85
                    ),
                    array(
                        'text' => 'Addressing key challenges in ' . $user_industry . ' sector across Africa',
                        'type' => 'Focus Area',
                        'confidence' => 90
                    ),
                    array(
                        'text' => 'Leveraging local partnerships and cultural understanding',
                        'type' => 'Competitive Advantage',
                        'confidence' => 80
                    )
                );
                break;
                
            case 'market_analysis':
                $suggestions = array(
                    array(
                        'text' => 'Growing ' . $user_industry . ' market in ' . chatgabi_get_country_name_from_code($user_country) . ' with 15-20% annual growth',
                        'type' => 'Market Size',
                        'confidence' => 75
                    ),
                    array(
                        'text' => 'Target demographics: Urban middle class, SME businesses, tech-savvy consumers',
                        'type' => 'Target Market',
                        'confidence' => 85
                    ),
                    array(
                        'text' => 'Key competitors: ' . implode(', ', array_slice($market_examples['main_competitors'] ?? array(), 0, 3)),
                        'type' => 'Competition',
                        'confidence' => 70
                    )
                );
                break;
                
            case 'financial_projections':
                $currency = chatgabi_get_country_currency($user_country);
                $suggestions = array(
                    array(
                        'text' => 'Initial investment: ' . $currency . ' 50,000 - 100,000',
                        'type' => 'Startup Capital',
                        'confidence' => 80
                    ),
                    array(
                        'text' => 'Break-even timeline: 12-18 months',
                        'type' => 'Timeline',
                        'confidence' => 75
                    ),
                    array(
                        'text' => 'Year 3 revenue target: ' . $currency . ' 500,000 - 1,000,000',
                        'type' => 'Revenue Goal',
                        'confidence' => 70
                    )
                );
                break;
        }
    }
    
    return $suggestions;
}

/**
 * Process AI enhancement request for template fields
 */
function chatgabi_process_ai_field_enhancement($field_id, $action, $current_content, $user_context) {
    // Check user credits
    $user_id = get_current_user_id();
    $current_credits = get_user_meta($user_id, 'businesscraft_credits', true) ?: 0;
    $required_credits = chatgabi_get_enhancement_credit_cost($action);
    
    if ($current_credits < $required_credits) {
        return new WP_Error(
            'insufficient_credits',
            sprintf(__('Insufficient credits. Required: %d, Available: %d', 'chatgabi'), $required_credits, $current_credits)
        );
    }
    
    // Build enhancement prompt
    $prompt = chatgabi_build_field_enhancement_prompt($action, $current_content, $user_context);
    
    // Call OpenAI API
    $response = businesscraft_ai_process_openai_request($prompt, $user_id, array(
        'max_tokens' => 400,
        'temperature' => 0.7
    ));
    
    if (is_wp_error($response)) {
        return $response;
    }
    
    // Deduct credits
    $new_credits = $current_credits - $required_credits;
    update_user_meta($user_id, 'businesscraft_credits', $new_credits);
    
    // Log enhancement usage
    chatgabi_log_ai_enhancement_usage($user_id, $field_id, $action, $required_credits);
    
    return array(
        'enhanced_content' => $response['response'],
        'original_content' => $current_content,
        'enhancement_type' => $action,
        'credits_used' => $required_credits,
        'remaining_credits' => $new_credits,
        'suggestions' => chatgabi_extract_enhancement_suggestions($response['response'])
    );
}

/**
 * Build enhancement prompt for specific field actions
 */
function chatgabi_build_field_enhancement_prompt($action, $content, $user_context) {
    $country_name = chatgabi_get_country_name_from_code($user_context['country'] ?? 'GH');
    $industry = $user_context['industry'] ?? 'general business';
    
    $base_context = "You are an expert business consultant specializing in African markets, particularly {$country_name}. ";
    $base_context .= "You have deep knowledge of the {$industry} industry and local business practices. ";
    
    switch ($action) {
        case 'improve_description':
            return $base_context . "Improve this business description to be more compelling and specific to the {$country_name} market:\n\n{$content}\n\nProvide an enhanced version that highlights unique value propositions and local market relevance.";
            
        case 'add_local_context':
            return $base_context . "Add specific local context and cultural considerations to this content for the {$country_name} market:\n\n{$content}\n\nInclude relevant local business practices, cultural nuances, and market-specific opportunities.";
            
        case 'generate_market_data':
            return $base_context . "Generate realistic market analysis data for the {$industry} industry in {$country_name}. Include market size, growth rates, key segments, and trends. Base this on:\n\n{$content}";
            
        case 'competitor_analysis':
            return $base_context . "Provide a competitive landscape analysis for the {$industry} sector in {$country_name}. Include major players, market positioning, and competitive advantages. Context:\n\n{$content}";
            
        case 'generate_projections':
            return $base_context . "Create realistic financial projections for a {$industry} business in {$country_name}. Include revenue forecasts, expense estimates, and key assumptions. Business context:\n\n{$content}";
            
        case 'validate_assumptions':
            return $base_context . "Review and validate these financial assumptions for a {$industry} business in {$country_name}. Provide feedback on realism and suggest improvements:\n\n{$content}";
            
        default:
            return $base_context . "Enhance this content to be more professional, detailed, and relevant to the {$country_name} {$industry} market:\n\n{$content}";
    }
}

/**
 * Get credit cost for different enhancement actions
 */
function chatgabi_get_enhancement_credit_cost($action) {
    $costs = array(
        'improve_description' => 1,
        'add_local_context' => 1,
        'generate_market_data' => 2,
        'competitor_analysis' => 2,
        'generate_projections' => 2,
        'validate_assumptions' => 1,
        'improve_content' => 1
    );
    
    return $costs[$action] ?? 1;
}

/**
 * Extract actionable suggestions from AI response
 */
function chatgabi_extract_enhancement_suggestions($ai_response) {
    // Simple extraction of bullet points or numbered lists
    $suggestions = array();
    
    // Look for bullet points
    if (preg_match_all('/[•\-\*]\s*(.+)/', $ai_response, $matches)) {
        foreach ($matches[1] as $suggestion) {
            $suggestions[] = array(
                'text' => trim($suggestion),
                'type' => 'AI Suggestion'
            );
        }
    }
    
    // Look for numbered lists
    if (preg_match_all('/\d+\.\s*(.+)/', $ai_response, $matches)) {
        foreach ($matches[1] as $suggestion) {
            $suggestions[] = array(
                'text' => trim($suggestion),
                'type' => 'AI Recommendation'
            );
        }
    }
    
    return array_slice($suggestions, 0, 5); // Limit to 5 suggestions
}

/**
 * Log AI enhancement usage for analytics
 */
function chatgabi_log_ai_enhancement_usage($user_id, $field_id, $action, $credits_used) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'chatgabi_ai_enhancement_log';
    
    $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'field_id' => $field_id,
            'enhancement_action' => $action,
            'credits_used' => $credits_used,
            'created_at' => current_time('mysql')
        ),
        array('%d', '%s', '%s', '%d', '%s')
    );
}

/**
 * Get country currency symbol
 */
function chatgabi_get_country_currency($country_code) {
    $currencies = array(
        'GH' => 'GHS',
        'KE' => 'KES',
        'NG' => 'NGN',
        'ZA' => 'ZAR'
    );
    
    return $currencies[$country_code] ?? 'USD';
}
